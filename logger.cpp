#include "logger.h"
//#include "logger_p.h"
#include <QCoreApplication>
#include "log4qt/logmanager.h"
#include "log4qt/propertyconfigurator.h"
#include "log4qt/helpers/properties.h"
#include <QDebug>

Log4Qt::Level toLog4Level(LogLevel level)
{
    // 正确映射Logger::LogLevel到Log4Qt标准级别
    switch (level) {
        case Trace:
            return Log4Qt::Level::TRACE_INT;  // 5000
        case Debug:
            return Log4Qt::Level::DEBUG_INT;  // 10000
        case Info:
            return Log4Qt::Level::INFO_INT;   // 20000
        case Warn:
            return Log4Qt::Level::WARN_INT;   // 30000
        case Error:
            return Log4Qt::Level::ERROR_INT;  // 40000
        case Fatal:
            return Log4Qt::Level::FATAL_INT;  // 50000
        default:
            return Log4Qt::Level::INFO_INT;   // 默认INFO级别
    }
}
Logger Logger::instance;
QString Logger::m_logPath;
Log4Qt::Logger *Logger::rootLogger;

Logger Logger::getInstance()
{
    return instance;
}

void Logger::init(const QString &logPath)
{
    if(m_isInited) return;
    m_logPath=logPath;
    rootLogger=Log4Qt::Logger::rootLogger();
    Log4Qt::Properties props;
    props.setProperty("log4j.threshold", "ALL");
    props.setProperty("log4j.handleQtMessages", "true");
    props.setProperty("log4j.rootLogger", "ALL, console, daily");
    props.setProperty("log4j.appender.console", "org.apache.log4j.ConsoleAppender");
    props.setProperty("log4j.appender.console.target", "STDOUT_TARGET");
    props.setProperty("log4j.appender.console.layout", "org.apache.log4j.TTCCLayout");

    props.setProperty("log4j.appender.console.layout.dateFormat", "dd.MM.yyyy hh:mm:ss.zzz");
    props.setProperty("log4j.appender.console.layout.contextPrinting", "true");
    props.setProperty("log4j.appender.console.threshold", "ALL");

    props.setProperty("log4j.appender.daily", "org.apache.log4j.DailyFileAppender");
    QString path = m_logPath + "/${yyyy_MM_dd}.log";
    props.setProperty("log4j.appender.daily.file", path);
    props.setProperty("log4j.appender.daily.appendFile", "true");
    props.setProperty("log4j.appender.daily.datePattern", "yyyy_MM_dd");
    props.setProperty("log4j.appender.daily.keepDays", "90");

    //与console输出格式一致
    // props.setProperty("log4j.appender.daily.layout","${log4j.appender.console.layout}");
    // props.setProperty("log4j.appender.daily.layout.dateFormat","${log4j.appender.console.layout.dateFormat}");
    //-------------------

    props.setProperty("log4j.appender.daily.layout", "org.apache.log4j.PatternLayout");
    props.setProperty("log4j.appender.daily.layout.ConversionPattern", "[%d{yyyy-MM-dd HH:mm:ss.zzz}] [%-5p]  %m%n");
    // props.setProperty("log4j.appender.daily.layout.ConversionPattern","%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L %m%n");
    props.setProperty("log4j.appender.daily.layout.contextPrinting", "true");

    //------------------------------------

    //    props.setProperty("log4j.appender.database", "org.apache.log4j.DatabaseAppender");
    //    props.setProperty("log4j.appender.database.table", "log_entries");
    //    props.setProperty("log4j.appender.database.connection", "adalog");
    //    props.setProperty("log4j.appender.database.layout", "org.apache.log4j.DatabaseLayout");
    //    props.setProperty("log4j.appender.database.layout.timeStampColumn", "time_stamp");
    //    props.setProperty("log4j.appender.database.layout.loggenameColumn", "logger_name");
    //    props.setProperty("log4j.appender.database.layout.threadNameColumn", "thread_name");
    //    props.setProperty("log4j.appender.database.layout.levelColumn", "level");
    //    props.setProperty("log4j.appender.database.layout.messageColumn", "message");

    //    props.setProperty("log4j.appender.database.threshold ", " ALL");
    //    props.setProperty("log4j.appender.database.layout ", " org.apache.log4j.PatternLayout");
    //    props.setProperty("log4j.appender.database.layout.ConversionPattern ",
    //                      " % d{ yyyy - MM - dd HH : mm : ss } % -5p % c{ 1 } : % L - % m % n");
    //    props.setProperty("log4j.appender.database.URL ", " jdbc : postgresql: // localhost:5432/adalog");
    //    props.setProperty("log4j.appender.database.user ", " postgres");
    //    props.setProperty("log4j.appender.database.password ", " 1");
    //    props.setProperty("log4j.appender.database.driver ", " org.postgresql.Driver");
    //    props.setProperty("log4j.appender.database.sql ",
    //                      " INSERT INTO log_entries(log_level, log_message, log_timestamp) VALUES(% p, '%m', NOW())");

    //------------------------------------
    props.setProperty("log4j.logger.LoggerObjectPrio", "ERROR, daily, console");
    props.setProperty("log4j.additivity.LoggerObjectPrio", "false");

    props.setProperty("log4j.logger.commLogger", "ALL, commDaily");
    props.setProperty("log4j.additivity.commLogger", "false");
    QString path2=m_logPath+"/comm_${yyyy-MM-dd}.log";
    props.setProperty("log4j.appender.commDaily", "org.apache.log4j.DailyFileAppender");
    props.setProperty("log4j.appender.commDaily.file", path2);
    props.setProperty("log4j.appender.commDaily.appendFile", "true");
    props.setProperty("log4j.appender.commDaily.datePattern", "yyyy_MM_dd");
    props.setProperty("log4j.appender.commDaily.keepDays", "90");
    props.setProperty("log4j.appender.commDaily.layout", "org.apache.log4j.TTCCLayout");
    props.setProperty("log4j.appender.commDaily.layout.dateFormat", "yyyy.MM.dd hh:mm:ss.zzz");
    props.setProperty("log4j.appender.commDaily.layout.contextPrinting", "true");

    m_isInited=Log4Qt::PropertyConfigurator::configure(props);
}

void Logger::log(LogLevel level, const QString &loggerName, const QString &text)
{
    rootLogger->logger(loggerName)->log(toLog4Level(level), text);
}

Log4Qt::Logger* Logger::getRootLogger(){
    return rootLogger;
}
