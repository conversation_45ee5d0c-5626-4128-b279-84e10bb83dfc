#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "logger.h"
#include "streamlined_log4qt_source.h"

/**
 * @brief 测试日志级别映射修复
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "\n=== 测试日志级别映射修复 ===";
    
    // 1. 初始化Log4QT
    qDebug() << "1. 初始化Log4QT...";
    Logger::init("./logs");
    qDebug() << "✓ Log4QT初始化完成";
    
    // 2. 创建StreamlinedLog4QtSource并连接信号
    qDebug() << "2. 创建StreamlinedLog4QtSource...";
    StreamlinedLog4QtSource source;
    
    int receivedCount = 0;
    QObject::connect(&source, &IDataSource::dataReady, 
                     [&receivedCount](const QVector<LogEntry>& entries) {
        receivedCount += entries.size();
        qDebug() << QString("✓ 接收到 %1 条日志，总计: %2").arg(entries.size()).arg(receivedCount);
        for (const LogEntry& entry : entries) {
            qDebug() << QString("  - [%1] %2: %3")
                        .arg(entry.levelString())
                        .arg(entry.source())
                        .arg(entry.message());
        }
    });
    
    QObject::connect(&source, &IDataSource::error,
                     [](const QString& error) {
        qDebug() << "✗ 错误:" << error;
    });
    
    // 3. 连接到Log4QT
    qDebug() << "3. 连接到Log4QT...";
    source.setLoggerName("root");
    bool connected = source.connectToSource();
    qDebug() << "连接结果:" << (connected ? "✓ 成功" : "✗ 失败");
    
    if (!connected) {
        qDebug() << "连接失败，退出测试";
        return 1;
    }
    
    // 4. 测试日志输出
    qDebug() << "4. 开始测试日志级别映射...";
    
    qDebug() << "\n--- 输出测试日志 ---";
    DEBUG("测试DEBUG级别日志");
    INFO("测试INFO级别日志");  
    WARN("测试WARN级别日志");
    ERROR("测试ERROR级别日志");
    FATAL("测试FATAL级别日志");
    qDebug() << "--- 日志输出完成 ---\n";
    
    // 5. 等待接收结果
    QTimer::singleShot(2000, [&]() {
        qDebug() << "\n=== 测试结果 ===";
        qDebug() << QString("总共接收到的日志条数: %1").arg(receivedCount);
        
        if (receivedCount >= 5) {  // 期望至少接收到5条日志
            qDebug() << "✓ 测试成功！日志级别映射修复有效";
            qDebug() << "✓ StreamlinedLogAppender能够正确捕获Log4QT输出";
        } else {
            qDebug() << "✗ 测试失败！仍然无法捕获Log4QT输出";
            qDebug() << "可能的原因：";
            qDebug() << "- Log4QT配置问题";
            qDebug() << "- Appender绑定问题";
            qDebug() << "- 级别过滤问题";
        }
        
        app.quit();
    });
    
    return app.exec();
}
